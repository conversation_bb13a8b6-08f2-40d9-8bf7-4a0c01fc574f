import { motion } from "framer-motion";
import Image from "next/image";
import { ETH, BitcoinIcon, UsdtIcon } from "../../../../public/assets";

export default function SkeletonOne() {
  const variants = {
    initial: {
      x: 0,
    },
    animate: {
      x: 10,
      rotate: 5,
      transition: {
        duration: 0.2,
      },
    },
  };
  const variantsSecond = {
    initial: {
      x: 0,
    },
    animate: {
      x: -10,
      rotate: -5,
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <motion.div
      initial="initial"
      whileHover="animate"
      className="flex flex-1 w-full h-full min-h-[6rem] dark:bg-dot-white/[0.2] bg-dot-black/[0.2] flex-col space-y-2"
    >
      <motion.div
        variants={variants}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2  items-center space-x-2 bg-white dark:bg-[#020817]"
      >
        <Image
          src={BitcoinIcon}
          alt="img"
          className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0"
        />
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-[#020817]"></div>
      </motion.div>
      <motion.div
        variants={variantsSecond}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 w-3/4 ml-auto bg-white dark:bg-[#020817]"
      >
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-[#020817]" />
        <Image
          src={ETH}
          alt=""
          className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0"
        />
      </motion.div>
      <motion.div
        variants={variants}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 bg-white dark:bg-[#020817]"
      >
        <Image
          src={UsdtIcon}
          alt=""
          className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0"
        />
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-[#020817]" />
      </motion.div>
    </motion.div>
  );
}
