{"name": "crypto-aggregator-dapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@nextui-org/react": "^2.4.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tabler/icons-react": "^3.10.0", "axios": "^1.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.4.5", "execp": "^0.0.1", "framer-motion": "^11.3.2", "fs": "^0.0.1-security", "lucide-react": "^0.408.0", "millify": "^6.1.0", "next": "14.2.5", "next-themes": "^0.3.0", "path": "^0.12.7", "react": "^18", "react-dom": "^18", "recharts": "^2.12.7", "request": "^2.88.2", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zustand": "^4.5.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^8", "eslint-config-next": "14.2.5"}}