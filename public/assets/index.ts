import Arbitrum from "./icons/arbitrum.svg";
import Avalanche from "./icons/Avalance.svg";
import Base from "./icons/Base.svg";
import BSC from "./icons/BinanceSmartChain.svg";
import ETH from "./icons/Ethereum.svg";
import Optimism from "./icons/Optimism.svg";
import Polygon from "./icons/Polygon.svg";
import MarginTrade from "./icons/MarginTrade.svg";
import MobileApp from "./icons/MobileApp.svg";
import News from "./icons/News.svg";
import TradeAPI from "./icons/TradeAPI.svg";
import Wallet from "./icons/Wallet.svg";
import Widgets from "./icons/Widget.svg";
import MetamaskWallet from "./wallets/MetamaskWallet.svg";
import PhantomWallet from "./wallets/PhantomWallet.svg";
import CoinbaseWallet from "./wallets/CoinbaseWallet.svg";
import WalletConnectWallet from "./wallets/WalletConnectWallet.svg";
import Oxose from "./oxose.jpeg";
import BitcoinIcon from "./icons/BitcoinIcon.svg";
import UsdtIcon from "./icons/UsdtIcon.svg";
import SolanaIcon from "./icons/SolanaIcon.svg";
import UsdcIcon from "./icons/UsdcIcon.svg";
import TronIcon from "./icons/TronIcon.svg";
import Squre1 from "./icons/square1.svg";
import Squre2 from "./icons/square2.svg";
import Squre3 from "./icons/square3.svg";
import Squre4 from "./icons/square4.svg";
import Img1 from "./screenshots/1.png";
import Img2 from "./screenshots/2.png";
import Img3 from "./screenshots/3.png";
import Img4 from "./screenshots/4.png";
import placeholder from "../homeImg.svg";

export {
  Arbitrum,
  Avalanche,
  Base,
  BSC,
  ETH,
  Optimism,
  Polygon,
  MarginTrade,
  MobileApp,
  News,
  TradeAPI,
  Wallet,
  Widgets,
  MetamaskWallet,
  PhantomWallet,
  CoinbaseWallet,
  WalletConnectWallet,
  Oxose,
  BitcoinIcon,
  UsdtIcon,
  SolanaIcon,
  UsdcIcon,
  TronIcon,
  Squre1,
  Squre2,
  Squre3,
  Squre4,
  Img1,
  Img2,
  Img3,
  Img4,
  placeholder,
};
