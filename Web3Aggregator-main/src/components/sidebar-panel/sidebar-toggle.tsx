import { ChevronLeft } from "lucide-react";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";

interface SidebarToggleProps {
  // Prop to determine if the sidebar is open or not
  isOpen: boolean | undefined;

  // Function to toggle the state of the sidebar
  setIsOpen?: () => void;
}

export function SidebarToggle({ isOpen, setIsOpen }: SidebarToggleProps) {
  return (
    // Container for the toggle button with visibility settings
    <div className="invisible lg:visible absolute top-[12px] -right-[16px] z-20 bg-white dark:bg-primary-foreground">
      <Button
        onClick={() => setIsOpen?.()}
        className="rounded-md w-8 h-8"
        variant="outline"
        size="icon"
      >
        <ChevronLeft
          className={cn(
            // Chevron icon with rotation animation based on isOpen prop
            "h-4 w-4 transition-transform ease-in-out duration-700",
            isOpen === false ? "rotate-180" : "rotate-0"
          )}
        />
      </Button>
    </div>
  );
}
