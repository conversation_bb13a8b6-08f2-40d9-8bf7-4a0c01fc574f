## Web3 Crypto Aggregator DApp

This project is a web3 data aggregator Decentralized Application (DApp) built using Next.js and React. It provides users with a user-friendly and modern UI for aggregating web3 data. The DApp is designed to be responsive on both desktop and mobile devices.

### Getting Started

1. Install dependencies:

   ```bash
   npm install
   ```

2. Run the development server:

   ```bash
   npm run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser to view the application.

### Features

- Aggregates data from coin-gecko api.
- Provides a clean and intuitive user interface.
- Supports responsive design for mobile and desktop.

### Technologies Used

- Next.js
- React
- Tailwind CSS
- TypeScript
- Millify
- Framer Motion

### Folder Structure

- `src/`: Contains the main source code for the application.
- `public/`: Includes static assets like images and SVG icons.
- `components/`: Reusable UI components used throughout the application.
- `features/`: Contains specific features like coins data and details components.
- `app/`: Includes layout components and global styles.
